import { UsageCheckResult, LimitType } from '@/types/subscription.types';
import { formatLimitMessage, getUpgradeSuggestion } from '@/lib/subscription-middleware';
import SubscriptionBadge from './SubscriptionBadge';
import { SubscriptionTier } from '@/types/schema.types';

interface UsageProgressBarProps {
  usage: UsageCheckResult;
  limitType: LimitType;
  className?: string;
}

export function UsageProgressBar({ usage, limitType, className = '' }: UsageProgressBarProps) {
  const { current, limit, remaining } = usage;

  // Handle unlimited case
  if (limit === -1) {
    return (
      <div className={`${className}`}>
        <div className="flex items-center justify-between mb-1">
          <span className="text-sm font-medium capitalize">{limitType.replace('_', ' ')}</span>
          <span className="text-sm text-text-secondary">{current} (unlimited)</span>
        </div>
        <div className="w-full bg-gray-700 rounded-full h-2">
          <div className="bg-primary h-2 rounded-full" style={{ width: '100%' }}></div>
        </div>
        <div className="text-xs text-text-secondary mt-1">Unlimited usage</div>
      </div>
    );
  }

  const percentage = Math.min((current / limit) * 100, 100);
  const isNearLimit = percentage >= 80;
  const isAtLimit = remaining === 0;

  // Color based on usage percentage
  let barColor = 'bg-primary';
  if (isAtLimit) {
    barColor = 'bg-red-500';
  } else if (isNearLimit) {
    barColor = 'bg-yellow-500';
  }

  return (
    <div className={`${className}`}>
      <div className="flex items-center justify-between mb-1">
        <span className="text-sm font-medium capitalize">{limitType.replace('_', ' ')}</span>
        <span className="text-sm text-text-secondary">{current}/{limit}</span>
      </div>
      <div className="w-full bg-gray-700 rounded-full h-2">
        <div
          className={`${barColor} h-2 rounded-full transition-all duration-300`}
          style={{ width: `${percentage}%` }}
        ></div>
      </div>
      <div className="flex items-center justify-between mt-1">
        <div className="text-xs text-text-secondary">
          {remaining > 0 ? `${remaining} remaining` : 'Limit reached'}
        </div>
        {isNearLimit && (
          <div className="text-xs text-yellow-400">
            {isAtLimit ? 'Upgrade needed' : 'Near limit'}
          </div>
        )}
      </div>
    </div>
  );
}

interface UsageStatsCardProps {
  title: string;
  usage: UsageCheckResult;
  limitType: LimitType;
  icon?: string;
  className?: string;
}

export function UsageStatsCard({
  title,
  usage,
  limitType,
  icon,
  className = ''
}: UsageStatsCardProps) {
  const { current, limit, remaining } = usage;
  const isAtLimit = remaining === 0 && limit !== -1;
  const isNearLimit = limit !== -1 && (current / limit) >= 0.8;

  return (
    <div className={`bg-background border border-gray-800 rounded-lg p-4 ${className}`}>
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center gap-2">
          {icon && <span className="text-lg">{icon}</span>}
          <h3 className="font-medium text-sm">{title}</h3>
        </div>
        {isAtLimit && (
          <span className="text-xs bg-red-500/20 text-red-400 px-2 py-1 rounded">
            Limit reached
          </span>
        )}
        {isNearLimit && !isAtLimit && (
          <span className="text-xs bg-yellow-500/20 text-yellow-400 px-2 py-1 rounded">
            Near limit
          </span>
        )}
      </div>

      <div className="text-2xl font-bold text-primary mb-2">
        {limit === -1 ? current : `${current}/${limit}`}
      </div>

      <UsageProgressBar usage={usage} limitType={limitType} className="mb-3" />

      {isAtLimit && (
        <div className="text-xs text-red-400 bg-red-500/10 p-2 rounded">
          {getUpgradeSuggestion(limitType)}
        </div>
      )}
    </div>
  );
}

interface UsageDashboardProps {
  worldsUsage?: UsageCheckResult;
  aiMessagesUsage: UsageCheckResult;
  subscriptionTier: SubscriptionTier;
  className?: string;
}

export function UsageDashboard({
  worldsUsage,
  aiMessagesUsage,
  subscriptionTier,
  className = ''
}: UsageDashboardProps) {
  return (
    <div className={`space-y-4 ${className}`}>
      <div className="flex items-center justify-between">
        <h2 className="text-lg font-medieval text-primary">Usage & Limits</h2>
        <SubscriptionBadge tier={subscriptionTier} size="sm" />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {worldsUsage && (
          <UsageStatsCard
            title="Worlds Created"
            usage={worldsUsage}
            limitType="messages"
            icon="🌍"
          />
        )}

        <UsageStatsCard
          title="Monthly Messages"
          usage={aiMessagesUsage}
          limitType="messages"
          icon="💬"
        />
      </div>

      {/* Reset info for daily limits */}
      {aiMessagesUsage.resetDate && (
        <div className="text-xs text-text-secondary bg-background-darker p-3 rounded border border-gray-800">
          <div className="flex items-center gap-2">
            <span>🔄</span>
            <span>AI message count resets daily at midnight</span>
          </div>
        </div>
      )}
    </div>
  );
}

interface CharacterLimitDisplayProps {
  usage: UsageCheckResult;
  worldName?: string;
  className?: string;
}

export function CharacterLimitDisplay({
  usage,
  worldName,
  className = ''
}: CharacterLimitDisplayProps) {
  const { current, limit, remaining } = usage;
  const isAtLimit = remaining === 0 && limit !== -1;

  return (
    <div className={`${className}`}>
      <div className="flex items-center justify-between mb-2">
        <span className="text-sm font-medium">
          Characters {worldName ? `in ${worldName}` : ''}
        </span>
        <span className="text-sm text-text-secondary">
          {limit === -1 ? `${current} (unlimited)` : `${current}/${limit}`}
        </span>
      </div>

      {limit !== -1 && (
        <UsageProgressBar usage={usage} limitType="messages" />
      )}

      {isAtLimit && (
        <div className="mt-2 text-xs text-red-400 bg-red-500/10 p-2 rounded">
          Character limit reached for this world. {getUpgradeSuggestion('messages')}
        </div>
      )}
    </div>
  );
}

interface LimitWarningProps {
  limitType: LimitType;
  current: number;
  limit: number;
  tier: SubscriptionTier;
  onUpgrade?: () => void;
  className?: string;
}

export function LimitWarning({
  limitType,
  current,
  limit,
  tier,
  onUpgrade,
  className = ''
}: LimitWarningProps) {
  return (
    <div className={`bg-red-500/10 border border-red-500/30 rounded-lg p-4 ${className}`}>
      <div className="flex items-start gap-3">
        <span className="text-red-400 text-xl">⚠️</span>
        <div className="flex-1">
          <h3 className="text-red-400 font-medium mb-1">
            {limitType.replace('_', ' ')} Limit Reached
          </h3>
          <p className="text-red-300 text-sm mb-3">
            You've reached your {limitType.replace('_', ' ')} limit ({current}/{limit}) for the {tier} tier.
          </p>
          <p className="text-red-300 text-sm mb-3">
            {getUpgradeSuggestion(limitType)}
          </p>
          {onUpgrade && (
            <button
              onClick={onUpgrade}
              className="bg-primary hover:bg-primary/80 text-white px-4 py-2 rounded text-sm font-medium transition-colors"
            >
              Upgrade Now
            </button>
          )}
        </div>
      </div>
    </div>
  );
}

interface QuickUsageIndicatorProps {
  usage: UsageCheckResult;
  limitType: LimitType;
  compact?: boolean;
  className?: string;
}

export function QuickUsageIndicator({
  usage,
  limitType,
  compact = false,
  className = ''
}: QuickUsageIndicatorProps) {
  const { current, limit, remaining } = usage;
  const isAtLimit = remaining === 0 && limit !== -1;
  const isNearLimit = limit !== -1 && (current / limit) >= 0.8;

  if (compact) {
    return (
      <span className={`text-xs ${isAtLimit ? 'text-red-400' : isNearLimit ? 'text-yellow-400' : 'text-text-secondary'} ${className}`}>
        {limit === -1 ? current : `${current}/${limit}`}
      </span>
    );
  }

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      <span className="text-sm text-text-secondary capitalize">
        {limitType.replace('_', ' ')}:
      </span>
      <span className={`text-sm font-medium ${isAtLimit ? 'text-red-400' : isNearLimit ? 'text-yellow-400' : 'text-text'}`}>
        {limit === -1 ? `${current} (unlimited)` : `${current}/${limit}`}
      </span>
      {isAtLimit && <span className="text-red-400 text-xs">⚠️</span>}
    </div>
  );
}
