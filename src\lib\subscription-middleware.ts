import { NextRequest, NextResponse } from 'next/server';
import { checkUsageLimit, enforceUsageLimit, incrementUsage } from './usage-tracking.service';
import { LimitType, SubscriptionLimitError } from '@/types/subscription.types';

/**
 * Middleware to check subscription limits before allowing actions
 * Note: In the new structure, only message limits are enforced
 */
export async function withSubscriptionLimit(
  userId: string,
  limitType: LimitType
) {
  return async function middleware(handler: Function) {
    try {
      // Check if the action is allowed
      await enforceUsageLimit({
        userId,
        limitType,
        increment: false // Don't increment yet, just check
      });

      // If allowed, execute the handler
      const result = await handler();

      // If handler was successful, increment the usage
      if (result && !result.error) {
        await incrementUsage(userId, limitType);
      }

      return result;
    } catch (error) {
      if (error instanceof SubscriptionLimitError) {
        return {
          error: error.message,
          code: 'SUBSCRIPTION_LIMIT_EXCEEDED',
          details: {
            limitType: error.limitType,
            current: error.current,
            limit: error.limit,
            tier: error.tier
          }
        };
      }
      throw error;
    }
  };
}

/**
 * Helper function to create limit-aware API responses
 */
export function createLimitResponse(error: SubscriptionLimitError): NextResponse {
  return NextResponse.json(
    {
      error: error.message,
      code: 'SUBSCRIPTION_LIMIT_EXCEEDED',
      details: {
        limitType: error.limitType,
        current: error.current,
        limit: error.limit,
        tier: error.tier,
        upgradeRequired: true
      }
    },
    { status: 429 } // Too Many Requests
  );
}

/**
 * Wrapper for API routes that need subscription limit checking
 * Note: In the new structure, only message limits are enforced
 */
export function withSubscriptionLimitAPI(
  handler: (request: NextRequest, context: any) => Promise<NextResponse>,
  getLimitConfig: (request: NextRequest, context: any) => Promise<{
    userId: string;
    limitType: LimitType;
  }>
) {
  return async function wrappedHandler(request: NextRequest, context: any): Promise<NextResponse> {
    try {
      const { userId, limitType } = await getLimitConfig(request, context);

      // Check if the action is allowed
      const limitCheck = await checkUsageLimit({
        userId,
        limitType,
        increment: false
      });

      if (!limitCheck.allowed) {
        return NextResponse.json(
          {
            error: `${limitType} limit exceeded`,
            code: 'SUBSCRIPTION_LIMIT_EXCEEDED',
            details: {
              limitType,
              current: limitCheck.current,
              limit: limitCheck.limit,
              remaining: limitCheck.remaining,
              upgradeRequired: true
            }
          },
          { status: 429 }
        );
      }

      // Execute the original handler
      const response = await handler(request, context);

      // If successful (2xx status), increment usage
      if (response.ok) {
        try {
          await incrementUsage(userId, limitType);
        } catch (incrementError) {
          console.error('Error incrementing usage after successful operation:', incrementError);
          // Don't fail the request if increment fails, just log it
        }
      }

      return response;
    } catch (error) {
      console.error('Error in subscription limit middleware:', error);

      if (error instanceof SubscriptionLimitError) {
        return createLimitResponse(error);
      }

      // Re-throw other errors to be handled by the normal error handling
      throw error;
    }
  };
}

/**
 * Helper to check limits without enforcement (for UI display)
 * Note: In the new structure, only message limits are enforced
 */
export async function checkLimitsForDisplay(userId: string) {
  try {
    const messagesCheck = await checkUsageLimit({ userId, limitType: 'messages' });

    return {
      messages: messagesCheck
    };
  } catch (error) {
    console.error('Error checking limits for display:', error);
    return null;
  }
}

/**
 * Helper functions for getting counts (no limits enforced in new structure)
 */
export async function getDisplayCounts(userId: string) {
  try {
    const { getUserWorldCount, getUserCharacterCount } = await import('./usage-tracking.service');

    const [worldCount, characterCount] = await Promise.all([
      getUserWorldCount(userId),
      getUserCharacterCount(userId)
    ]);

    return {
      worlds: {
        allowed: true,
        current: worldCount,
        limit: -1, // Unlimited
        remaining: -1,
        message: undefined
      },
      characters: {
        allowed: true,
        current: characterCount,
        limit: -1, // Unlimited
        remaining: -1,
        message: undefined
      }
    };
  } catch (error) {
    console.error('Error getting display counts:', error);
    return null;
  }
}

/**
 * Utility to format limit messages for UI
 */
export function formatLimitMessage(
  limitType: LimitType,
  current: number,
  limit: number,
  remaining: number
): string {
  if (limit === -1) {
    return `${current} ${limitType} (unlimited)`;
  }

  const percentage = (current / limit) * 100;

  if (remaining === 0) {
    return `Limit reached: ${current}/${limit} ${limitType}`;
  } else if (percentage >= 80) {
    return `${current}/${limit} ${limitType} (${remaining} remaining)`;
  } else {
    return `${current}/${limit} ${limitType}`;
  }
}

/**
 * Get upgrade suggestion based on limit type
 * Note: In the new structure, only message limits exist
 */
export function getUpgradeSuggestion(limitType: LimitType): string {
  switch (limitType) {
    case 'messages':
      return 'Upgrade to Pro for 100 monthly messages or Pro+ for 500 monthly messages';
    default:
      return 'Upgrade your subscription for higher limits';
  }
}

/**
 * Check if user is approaching any limits (80% threshold)
 * Note: In the new structure, only message limits are checked
 */
export async function checkApproachingLimits(userId: string) {
  try {
    const limits = await checkLimitsForDisplay(userId);
    if (!limits) return [];

    const approaching = [];

    // Check message limit
    if (limits.messages.limit !== -1) {
      const messagesPercentage = (limits.messages.current / limits.messages.limit) * 100;
      if (messagesPercentage >= 80) {
        approaching.push({
          type: 'messages' as LimitType,
          percentage: messagesPercentage,
          current: limits.messages.current,
          limit: limits.messages.limit,
          message: formatLimitMessage('messages', limits.messages.current, limits.messages.limit, limits.messages.remaining)
        });
      }
    }

    return approaching;
  } catch (error) {
    console.error('Error checking approaching limits:', error);
    return [];
  }
}
